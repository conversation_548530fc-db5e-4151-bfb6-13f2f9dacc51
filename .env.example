NODE_ENV=development
APP_PORT=4000
APP_PREFIX=api/v1
APP_NAME=moonbags
API_URI=http://localhost:4000

MONGODB_URI=mongodb://root:1@localhost:27017/moonbags_dev?authSource=admin&replicaSet=rs0

REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

KAFKA_CLIENT_ID=
KAFKA_BROKERS=
KAFKA_GROUP_ID=

WS_PORT=4001
PINATA_JWT=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QDs3qEqUODmwr8qoiFEci0I0rK93TWtnUoBVfO0C2XI
PINATA_GATEWAY=beige-abstract-pig-256.mypinata.cloud

# EVM config
EVM_RPC_URL=https://rpc.hyperliquid.xyz/evm
SHRO_TOKEN_ADDRESS=0x7718F3529425E09bF103e5dcf35B9BbB2148f420
SHRO_TREASURY_ADDRESS=0xb6981f22cB9A48B12483B7Fcf512Abd0C8a2B3de
MOONBAGS_LAUNCHPAD_ADDRESS=0xda6fCCed61457224e65927b373f57Dae98bF50a7
MOONBAGS_STAKE_ADDRESS=0x3247C027810d8b03846d5AEA6040198203E7Ffe9
TOKEN_LOCK_ADDRESS=0x4A2Fb87CeB887813B1214695Cde0F689c952c05c

# upload image config
IMAGE_STORAGE=pinata
GCP_BUCKET_NAME=moonbags
