/* eslint-disable @typescript-eslint/no-unused-vars */
import * as dotenv from 'dotenv';
dotenv.config();
import { NestFactory } from '@nestjs/core';
import { Injectable, Module } from '@nestjs/common';
import { SharedModule } from '@modules/shared/shared.module';
import { sleep } from '@shares/utils/common.utils';
import { TreasuryModule } from '@modules/treasury/treasury.module';
import { CoinRepository } from '@modules/coin/repositories/coin.repository';
import { Coin } from '@modules/coin/schemas/coin.schema';
import { CoinModule } from '@modules/coin/coin.module';
import { HolderModule } from '@modules/holder/holder.module';
import axios from 'axios';
import retry from 'async-retry';
import { HolderRepository } from '@modules/holder/repositories/holder.repository';
import { Decimal128 } from 'bson';
import { formatLaunchpadTokenAmount } from '@shares/utils/evm-utils';

const MAX_HOLDERS = 100;
const HYPERSCAN_API_URL = 'https://www.hyperscan.com/api/v2';
const API_RATE_LIMIT_DELAY = 250;

interface TopHolderData {
  address: string;
  balance: string;
}

@Injectable()
export class IndexerTotalHolder {
  constructor(
    private readonly coinRepository: CoinRepository,
    private readonly holderRepository: HolderRepository,
  ) {}

  async run() {
    while (true) {
      // const coins = await this.coinRepository.model
      //   .find({})
      //   .sort({ _id: 1 })
      //   .limit(1000);
      const coins = [
        {
          symbol: 'Wrapped HYPE',
          tokenAddress: '0x5555555555555555555555555555555555555555',
        },
      ] as Coin[];
      for (const coin of coins) {
        await this.processCoin(coin);
        await sleep(200);
      }

      console.log('Sleep 30 seconds to next tick');
      await sleep(30000);
    }
  }

  private async processCoin(coin: Coin) {
    try {
      console.log(`Processing coin: ${coin.symbol} (${coin.tokenAddress})`);

      await this.fetchAndSaveTopHolders(coin.tokenAddress);
      await this.updateTotalHolders(coin.tokenAddress);
    } catch (error: any) {
      console.error(
        `Error processing coin: ${coin.tokenAddress}`,
        error?.message,
      );
    }
  }

  /**
   * Fetch and save top 100 holders
   */
  async fetchAndSaveTopHolders(tokenAddress: string): Promise<void> {
    console.log(`Fetching top holders for token: ${tokenAddress}`);

    const holders: TopHolderData[] = [];
    let nextPageParams = null;

    while (true) {
      const requestConfig = {
        url: `${HYPERSCAN_API_URL}/tokens/${tokenAddress}/holders`,
        method: 'get',
        params: nextPageParams || undefined,
      };

      // Log the full URL
      const fullUrl = axios.getUri(requestConfig);
      console.log(`Making request to: ${fullUrl}`);

      const response = await retry(
        async () =>
          await axios.get(
            `${HYPERSCAN_API_URL}/tokens/${tokenAddress}/holders`,
            nextPageParams ? { params: nextPageParams } : undefined,
          ),
        {
          retries: 2,
          minTimeout: API_RATE_LIMIT_DELAY,
          maxTimeout: API_RATE_LIMIT_DELAY,
        },
      );

      const responseData = response.data;

      if (responseData.items) {
        const remainingSlots = MAX_HOLDERS - holders.length;
        const newHolders = responseData.items
          .slice(0, remainingSlots)
          .map((holder: any) => ({
            address: holder.address.hash,
            balance: formatLaunchpadTokenAmount(holder.value),
          }));

        holders.push(...newHolders);
      }
      console.log(
        'responseData.next_page_params: ',
        JSON.stringify(responseData.next_page_params, null, 2),
      );

      // Handle large numbers in next_page_params to avoid scientific notation
      if (responseData.next_page_params && responseData.next_page_params.value) {
        // Convert large number to string without scientific notation
        let valueStr: string;
        if (typeof responseData.next_page_params.value === 'number') {
          // Use toFixed(0) to avoid scientific notation for integers
          valueStr = responseData.next_page_params.value.toFixed(0);
        } else {
          valueStr = responseData.next_page_params.value.toString();
        }

        console.log(`Original value: ${responseData.next_page_params.value}, Converted value: ${valueStr}`);

        nextPageParams = {
          ...responseData.next_page_params,
          value: valueStr
        };
      } else {
        nextPageParams = responseData.next_page_params;
      }
      if (!nextPageParams || holders.length >= MAX_HOLDERS) break;
    }

    const topHolders = holders.slice(0, MAX_HOLDERS);

    await this.saveTopHolders(tokenAddress, topHolders);

    console.log(
      `Saved top ${topHolders.length} holders for token ${tokenAddress}`,
    );
  }

  private async updateTotalHolders(tokenAddress: string): Promise<void> {
    const response = await retry(
      async () =>
        await axios.get(`${HYPERSCAN_API_URL}/tokens/${tokenAddress}/counters`),
      {
        retries: 2,
        minTimeout: API_RATE_LIMIT_DELAY,
        maxTimeout: API_RATE_LIMIT_DELAY,
      },
    );

    const totalHolder = response.data.token_holders_count;

    await this.coinRepository.updateOne(
      { tokenAddress },
      {
        $set: {
          totalHolder,
        },
      },
    );
  }

  private async saveTopHolders(
    tokenAddress: string,
    holders: TopHolderData[],
  ): Promise<void> {
    await this.holderRepository.model.deleteMany({ tokenAddress });

    const holderDocs = holders.map((holder) => ({
      tokenAddress,
      userAddress: holder.address,
      amount: Decimal128.fromString(holder.balance),
      lastSyncedAt: Date.now(),
    }));

    if (holderDocs.length > 0) {
      await this.holderRepository.model.insertMany(holderDocs);
      console.log(
        `Saved ${holderDocs.length} top holders for token ${tokenAddress}`,
      );
    }
  }
}

@Module({
  imports: [SharedModule, TreasuryModule, CoinModule, HolderModule],
  providers: [IndexerTotalHolder],
  controllers: [],
})
class AppModule {}

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.init();
  const indexer = app.get(IndexerTotalHolder);
  await indexer.run();
}

bootstrap().catch((error) => {
  console.error(error);
  process.exit(1);
});
