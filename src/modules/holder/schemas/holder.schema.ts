import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { Decimal128 } from 'bson';

export type HolderDocument = Holder & Document;

@Schema({ timestamps: true, collection: 'holders' })
export class Holder extends Document {
  @Prop({ required: true })
  tokenAddress: string;

  @Prop({ required: true })
  userAddress: string;

  @Prop({ required: true })
  amount: Decimal128;

  @Prop({ required: true })
  lastSyncedAt: number;
}

export const HolderSchema = SchemaFactory.createForClass(Holder);

HolderSchema.plugin(mongoosePaginate);
HolderSchema.index(
  { tokenAddress: 1, userAddress: 1 },
  { unique: true, background: true },
);
HolderSchema.index({ tokenAddress: 1, amount: -1 }, { background: true });
HolderSchema.index({ userAddress: 1 }, { background: true });
