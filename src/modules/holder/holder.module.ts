import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Holder, HolderSchema } from './schemas/holder.schema';
import { HolderRepository } from './repositories/holder.repository';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Holder.name, schema: HolderSchema }]),
  ],
  providers: [HolderRepository],
  exports: [HolderRepository],
})
export class HolderModule {}
