import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { Holder, HolderDocument } from '../schemas/holder.schema';

@Injectable()
export class HolderRepository extends BaseRepository<Holder> {
  constructor(@InjectModel(Holder.name) model: PaginateModel<HolderDocument>) {
    super(model);
  }
}
