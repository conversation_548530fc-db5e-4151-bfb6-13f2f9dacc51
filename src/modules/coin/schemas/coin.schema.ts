import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { Document } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import mongooseAggregatePaginate from 'mongoose-aggregate-paginate-v2';
import { Decimal128, Double } from 'bson';

export type CoinDocument = Coin & Document;

export type StakedCoinDocument = CoinDocument & {
  replyCount: number;
  volumeUsd24h: string;
  includeStakedAmount: string;
  includeRewardAmount: string;
};

export type StakedSHROCoinDocument = StakedCoinDocument & {
  totalVolumeUsd24hAllCoin: string;
};

@Schema({ _id: false })
export class CoinSocial {
  @Prop()
  @Expose()
  @ApiProperty({
    type: String,
  })
  telegram?: string;

  @Prop()
  @Expose()
  @ApiProperty({
    type: String,
  })
  website: string;

  @Prop()
  @Expose()
  @ApiProperty({
    type: String,
  })
  x: string;
}

@Schema({ _id: false })
export class CoinRewardPool {
  @Prop()
  address: string;

  @Prop()
  stakedAmount?: Decimal128;

  @Prop()
  rewardClaimed?: Decimal128;
}

@Schema({ _id: false })
export class FeeRates {
  @Prop()
  platformFeeWithdrawRate: Decimal128;

  @Prop()
  creatorFeeWithdrawRate: Decimal128;

  @Prop()
  stakeFeeWithdrawRate: Decimal128;

  @Prop()
  platformStakeFeeWithdrawRate: Decimal128;
}

@Schema({ timestamps: true })
export class Coin extends Document {
  @Prop({ required: true })
  tokenAddress: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  symbol: string;

  @Prop({ required: false })
  slug: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  logoUri: string;

  @Prop({ type: CoinSocial, required: false })
  socials: CoinSocial;

  @Prop({ required: false })
  creatorAddress: string;

  @Prop({ required: false })
  replyCount: string;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  mcap: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  prevMcap: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  prevRealHypeReserves: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  mcapUsd: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  virtualHypeReserves: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  virtualTokenReserves: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  initVirtualHypeReserves: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  initVirtualTokenReserves: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  realHypeReserves: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  realTokenReserves: Decimal128;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  threshold: Decimal128;

  @Prop({ default: false })
  isKing: boolean;

  @Prop({ required: false })
  kingAt: Date;

  @Prop({ required: false, default: 0 })
  bondingCurve: Double;

  @Prop({ required: false, default: 0 })
  lastTrade: number;

  @Prop({ required: false, default: 0 })
  lastReply: number;

  // @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: false })
  listedAt: Date;

  @Prop({ required: false })
  listedPoolId: string;

  @Prop({ type: CoinRewardPool, required: false })
  rewardTokenPool?: CoinRewardPool;

  @Prop({ type: CoinRewardPool, required: false })
  rewardShroPool?: CoinRewardPool;

  @Prop({ type: CoinRewardPool, required: false })
  rewardCreatorPool?: CoinRewardPool;

  @Prop()
  totalRewards?: Decimal128;

  @Prop({ type: FeeRates, required: false })
  feeRates?: FeeRates;

  @Prop({ required: false, default: 0 })
  totalHolder: number;

  @Prop({ required: false, default: Decimal128.fromString('0') })
  volumeUsd24h: Decimal128;

  @Prop({ required: false, default: 0 })
  score: number;

  @Prop({ required: false, default: 0 })
  scoreUpdatedAt: number;

  @Prop({ required: false, default: 0 })
  boostFactor: number;

  @Prop({ required: false, default: null })
  bondingDex: string;
}

const CoinSchema = SchemaFactory.createForClass(Coin);

CoinSchema.plugin(mongoosePaginate);
CoinSchema.plugin(mongooseAggregatePaginate);
CoinSchema.index({ tokenAddress: 1 }, { unique: true, background: true });
CoinSchema.index({ slug: 1 }, { unique: true, background: true });
CoinSchema.index({ name: 'text', symbol: 'text', description: 'text' });
CoinSchema.index({ mcap: -1, prevMcap: 1 }, { background: true });
CoinSchema.index(
  { realHypeReserves: -1, prevRealHypeReserves: 1 },
  { background: true },
);
CoinSchema.index({ kingAt: -1, isKing: 1 }, { background: true });
CoinSchema.index({ listedAt: -1, listedPoolId: 1 }, { background: true });
CoinSchema.index({ createdAt: -1, tokenAddress: 1 }, { background: true });

CoinSchema.index(
  { tokenAddress: 1, creatorAddress: 1, mcapUsd: -1 },
  { background: true },
);
CoinSchema.index({ scoreUpdatedAt: -1, tokenAddress: 1 }, { background: true });
CoinSchema.index({ mcapUsd: -1, tokenAddress: 1 }, { background: true });
CoinSchema.index({ lastTrade: -1, tokenAddress: 1 }, { background: true });
CoinSchema.index({ lastReply: -1, tokenAddress: 1 }, { background: true });
export { CoinSchema };
