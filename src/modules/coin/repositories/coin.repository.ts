import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { Coin, CoinDocument } from '../schemas/coin.schema';

@Injectable()
export class CoinRepository extends BaseRepository<Coin> {
  constructor(@InjectModel(Coin.name) model: PaginateModel<CoinDocument>) {
    super(model);
  }



  async getTotalVolumeUsd24h() {
    const volumeUsd24h = await this.model.aggregate([
      { $group: { _id: null, totalVolumeUsd24h: { $sum: '$volumeUsd24h' } } },
    ]);
    return volumeUsd24h[0].totalVolumeUsd24h;
  }
}
