import {
  <PERSON>,
  Get,
  Param,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CoinService } from './coin.service';
import {
  CoinResponseDto,
  CoinsResponseDto,
  HolderDto,
  HoldersResponseDto,
  ListCoinDto,
} from './dtos/coin.dto';
import { plainToInstance } from 'class-transformer';
import { UserAddress } from '@shares/decorators/user.decorator';
import { PaginationDto } from '@shares/dtos/pagination.dto';
import { JwtAuthGuard } from '@modules/shared/auth/guards/auth.guard';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';
import { Request } from 'express';
import { getIpAddress } from 'src/utils/request.utils';

const CACHE_TTL = 1000; // 1 second

@ApiTags('Coin')
@Controller('coin')
export class CoinController {
  constructor(private readonly coinService: CoinService) {}

  @Get('king-of-the-hill')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get coin king of the hill' })
  @ApiResponse({
    status: 200,
    description: 'Return coin king of the hill',
  })
  async getCoinKingOfTheHill(@Req() req: Request) {
    console.log('Real IP:', getIpAddress(req));
    const data = await this.coinService.getCoinKingOfTheHill();
    return plainToInstance(CoinResponseDto, data, {
      excludeExtraneousValues: true,
    });
  }

  @Get()
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get list coin' })
  @ApiResponse({
    status: 200,
    description: 'Return list of coins',
  })
  async getCoinsWithPagination(@Query() query: ListCoinDto) {
    const data = await this.coinService.getCoinsWithPagination(query);
    return plainToInstance(CoinsResponseDto, data, {
      excludeExtraneousValues: true,
    });
  }

  @Get('holders')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get holders of a coin' })
  @ApiResponse({
    status: 200,
    type: HoldersResponseDto,
  })
  async getHolders(@Query() input: HolderDto) {
    const data = await this.coinService.getHolders(input);
    return plainToInstance(HoldersResponseDto, data, {
      excludeExtraneousValues: true,
    });
  }

  @ApiBearerAuth()
  @Get('my-coins')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @UseGuards(JwtAuthGuard)
  async getMyCoin(
    @UserAddress() address: string,
    @Query() query: PaginationDto,
  ) {
    const data = await this.coinService.getMyCoins(address, query);
    return plainToInstance(CoinsResponseDto, data, {
      excludeExtraneousValues: true,
    });
  }

  @Get(':identifier')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get detail coin by token address or slug' })
  @ApiResponse({
    status: 200,
    description: 'Return detail coins by token address or slug',
    type: CoinResponseDto,
  })
  async getByTokenAddressOrSlug(
    @Param('identifier') identifier: string,
  ): Promise<CoinResponseDto> {
    const data = await this.coinService.getByTokenAddressOrSlug(identifier);
    return plainToInstance(CoinResponseDto, data, {
      excludeExtraneousValues: true,
    });
  }
}
