/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { JwtPayload } from '@shares/dtos/jwt-payload.dto';
import { validateEvmAddress } from '@shares/utils/evm-utils';
import { ethers } from 'ethers';
import { UserRepository } from './repositories/user.repository';
import { User } from './schemas/user.schema';

@Injectable()
export class AuthService {
  constructor(
    private configService: ConfigService,
    private readonly jwtService: JwtService,
    private readonly userRepository: UserRepository,
  ) {}

  async verifySignature(
    signature: string,
    address: string,
    signMessage: string,
  ) {
    try {
      if (!validateEvmAddress(address)) {
        throw new Error('Invalid EVM address format');
      }

      const recoveredAddress = ethers.verifyMessage(signMessage, signature);

      if (recoveredAddress.toLowerCase() !== address.toLowerCase()) {
        throw new Error('Signature verification failed: address mismatch');
      }

      return true;
    } catch (error) {
      console.error('EVM signature verification error:', error?.message);
      throw new BadRequestException(error?.message || 'Invalid signature');
    }
  }

  async generateAccessToken(address) {
    const payload: JwtPayload = {
      address,
    };
    return this.jwtService.sign(
      { ...payload },
      {
        secret: this.configService.get('auth.jwtSecret'),
        expiresIn: this.configService.get('auth.jwtExpireIn'),
      },
    );
  }

  async saveUser(userData) {
    try {
      const user = await this.userRepository.findOneAndUpdate(
        {
          walletAddress: userData.address,
        },
        {
          walletAddress: userData.address,
        },
        { upsert: true, new: true },
      );
      return user as unknown as User;
    } catch (error) {
      console.log('Error saving user :', error);
      throw new Error('Error saving user');
    }
  }

  async saveUserByAddress(userAddress: string) {
    const user = await this.userRepository.findOneAndUpdate(
      {
        walletAddress: userAddress,
      },
      {
        walletAddress: userAddress,
      },
      { upsert: true, new: true },
    );
    return user as unknown as User;
  }

  async generateRefreshToken(address) {
    return this.jwtService.sign(
      { address },
      {
        secret: this.configService.get('auth.refreshTokenSecret'),
        expiresIn: this.configService.get('auth.refreshTokenExpireIn'),
      },
    );
  }

  async getUserByAddress(walletAddress: string) {
    const user = await this.userRepository.findOne({ walletAddress });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  async refreshToken(req) {
    const user = await this.getUserByAddress(req.user.address);
    console.log({ user });

    const newToken = await this.generateAccessToken(user.walletAddress);
    return { newToken };
  }
}
