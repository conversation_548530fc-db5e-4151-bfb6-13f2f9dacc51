import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { JwtPayload } from '@shares/dtos/jwt-payload.dto';
import { User } from '../schemas/user.schema';
import { IsRequiredEvmAddress } from '@shares/decorators/evm-address.decorator';

export class LoginRequestDto {
  @Expose()
  @IsRequiredEvmAddress({
    description: 'User wallet address',
  })
  address: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  signMessage: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  signature: string;
}

export class LoginResponseDto {
  accessToken: string;
  refreshToken: string;
  user: User | JwtPayload;
}

export class RefreshTokenResponseDto {
  @ApiProperty()
  @Expose()
  accessToken: string;

  @ApiProperty()
  @Expose()
  refreshToken: string;

  @ApiProperty()
  @Expose()
  expTime: number;
}
