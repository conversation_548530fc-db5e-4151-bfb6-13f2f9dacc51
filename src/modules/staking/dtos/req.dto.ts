import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEvmAddress,
  IsEvmAddressArray,
} from '@shares/decorators/evm-address.decorator';
import { Transform } from 'class-transformer';
import { <PERSON>N<PERSON><PERSON>, <PERSON>Optional, <PERSON>, <PERSON> } from 'class-validator';

export class StakingUserQueryDto {
  @ApiPropertyOptional({ default: 1 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ default: 10 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @IsEvmAddress({ description: 'Staking coin address' })
  stakingCoinAddress?: string;

  @IsEvmAddressArray({ description: 'User addresses to filter by' })
  userAddresses?: string[];
}
