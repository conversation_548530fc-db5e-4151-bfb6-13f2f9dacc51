import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose, Transform, Type } from 'class-transformer';
import { toStringBN } from 'src/utils/number.utils';

@Exclude()
export class StakingUserDto {
  @ApiProperty({
    description: 'The address of the staking coin',
    example: '0x1234567890abcdef',
  })
  @Expose()
  stakingCoinAddress: string;

  @ApiProperty({
    description: 'The address of the user',
    example: '0x1234567890abcdef',
  })
  @Expose()
  userAddress: string;

  @ApiProperty({
    description: 'The amount of staked',
    example: '100',
  })
  @Expose()
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  stakedAmount: string;

  @ApiProperty({
    description: 'The amount of claimed reward',
    example: '100',
  })
  @Expose()
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  rewardClaimed: string;

  @ApiProperty({
    description: 'The last stake date time',
    example: '2021-01-01T00:00:00.000Z',
  })
  @Expose()
  @Type(() => Number)
  @Transform(({ value }) => value || null)
  lastStake: number;
}
