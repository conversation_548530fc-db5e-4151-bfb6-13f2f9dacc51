import { StakingUserQueryDto } from '@modules/staking/dtos/req.dto';
import { StakingUserRepository } from '@modules/staking/repositories';
import { StakingUserDocument } from '@modules/staking/schemas';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MoonbagsStakingAbi } from '@shares/abis';
import {
  evmProvider,
  RETRY_MAX_ATTEMPT,
  RETRY_MAX_TIMEOUT,
  RETRY_MIN_TIMEOUT,
} from '@shares/utils/evm-provider';
import { formatHypeAmount } from '@shares/utils/evm-utils';
import retry from 'async-retry';
import { Decimal128 } from 'bson';
import { ethers } from 'ethers';
import { PaginateResult } from 'mongoose';

@Injectable()
export class StakingUserService {
  constructor(
    private readonly stakingUserRepository: StakingUserRepository,
    private readonly configService: ConfigService,
  ) {}

  async paginateByUserAddress(
    userAddress: string,
    query: StakingUserQueryDto,
  ): Promise<PaginateResult<StakingUserDocument>> {
    const { page = 1, limit = 10, stakingCoinAddress } = query;

    return this.stakingUserRepository.paginate(
      {
        userAddress,
        ...(stakingCoinAddress && { stakingCoinAddress }),
      },
      {
        page,
        limit,
        sort: { createdAt: -1 },
      },
    );
  }

  async getPublicListStakingUserByCoinAddress(
    stakingCoinAddress: string,
    query: StakingUserQueryDto,
  ) {
    const { page = 1, limit = 10 } = query;

    return this.stakingUserRepository.paginate(
      {
        ...(stakingCoinAddress && {
          stakingCoinAddress,
        }),
        ...(query.userAddresses && {
          userAddress: { $in: query.userAddresses },
        }),
        stakedAmount: { $gt: Decimal128.fromString('0') },
      },
      {
        page,
        limit,
        sort: { stakedAmount: -1 },
      },
    );
  }

  async getStakingUserByStakingCoinAddress(
    userAddress: string,
    stakingCoinAddress: string,
  ) {
    const stakingUser = await this.stakingUserRepository.findOne({
      userAddress,
      stakingCoinAddress,
    });

    if (!stakingUser) {
      throw new NotFoundException(
        `Staking user ${userAddress} not found for coin ${stakingCoinAddress}`,
      );
    }

    return stakingUser;
  }

  async getTotalDistributedAmountForUser(stakingUser: StakingUserDocument) {
    try {
      const stakingUserAddress = stakingUser.userAddress;
      const stakingCoinAddress = stakingUser.stakingCoinAddress;

      const moonbagsStakeAddress = this.configService.getOrThrow<string>(
        'app.moonbagsStakeAddress',
      );

      const contract = new ethers.Contract(
        moonbagsStakeAddress,
        MoonbagsStakingAbi,
        evmProvider,
      );

      const totalEarned = await retry(
        async () => {
          return await contract.calculateRewardsEarnedForUser(
            stakingCoinAddress,
            stakingUserAddress,
          );
        },
        {
          retries: RETRY_MAX_ATTEMPT,
          minTimeout: RETRY_MIN_TIMEOUT,
          maxTimeout: RETRY_MAX_TIMEOUT,
        },
      );
      const formattedAmount = formatHypeAmount(totalEarned);

      return formattedAmount;
    } catch (error) {
      console.error(
        `Error get total distributed amount for user ${stakingUser.userAddress}`,
        error?.message,
      );
      return '0';
    }
  }
}
