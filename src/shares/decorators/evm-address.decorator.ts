import { Transform } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { toChecksumAddress, validateEvmAddress } from '@shares/utils/evm-utils';

export function IsEvmAddress(options?: {
  description?: string;
  example?: string;
}) {
  return function (target: any, propertyKey: string) {
    ApiPropertyOptional({
      description: options?.description || 'EVM address',
    })(target, propertyKey);
    IsOptional()(target, propertyKey);
    IsString()(target, propertyKey);
    Transform(transformEvmAddress)(target, propertyKey);
  };
}

export function IsEvmAddressArray(options?: {
  description?: string;
  example?: string[];
}) {
  return function (target: any, propertyKey: string) {
    ApiPropertyOptional({
      type: [String],
      description: options?.description || 'Array of EVM addresses',
    })(target, propertyKey);
    IsOptional()(target, propertyKey);
    Transform(transformEvmAddressArray)(target, propertyKey);
  };
}

export function IsRequiredEvmAddress(options?: {
  description?: string;
  example?: string;
}) {
  return function (target: any, propertyKey: string) {
    ApiProperty({
      description: options?.description || 'EVM address',
    })(target, propertyKey);
    IsString()(target, propertyKey);
    IsNotEmpty()(target, propertyKey);
    Transform(transformEvmAddress)(target, propertyKey);
  };
}

export function transformEvmAddress({
  value,
}: {
  value: any;
}): string | undefined {
  if (!value) return value;
  const trimmed = value.trim();
  if (!validateEvmAddress(trimmed)) {
    throw new Error(`Invalid EVM address: ${trimmed}`);
  }
  return toChecksumAddress(trimmed);
}

export function transformEvmAddressArray({ value }: { value: any }): string[] {
  if (!value) return [];

  let addresses: string[];
  if (Array.isArray(value)) {
    addresses = value.map((item) => item.trim());
  } else {
    addresses = [value?.trim()];
  }

  // Validate and convert each address to checksum format
  return addresses.map((address) => {
    if (!address) return address;
    if (!validateEvmAddress(address)) {
      throw new Error(`Invalid EVM address: ${address}`);
    }
    return toChecksumAddress(address);
  });
}
