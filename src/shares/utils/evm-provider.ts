import { ethers } from 'ethers';
import retry from 'async-retry';
import AppConfig from 'src/configs/app.config';
import axios from 'axios';

const { evmRpcUrl } = AppConfig().app;

export const RETRY_MAX_ATTEMPT = 2;
export const RETRY_MIN_TIMEOUT = 100;
export const RETRY_MAX_TIMEOUT = 250;

export const evmProvider = new ethers.JsonRpcProvider(
  evmRpcUrl || 'https://rpc.hyperliquid.xyz/evm',
);

export const NETWORK_CONFIG = {
  CHAIN_ID: 999,
  NETWORK_NAME: 'HyperEVM Mainnet',
  RPC_URL: 'https://rpc.hyperliquid.xyz/evm',
  BLOCK_EXPLORER: 'https://explorer.hyperliquid.xyz',
};

// Token configuration
export const TOKEN_CONFIG = {
  HYPE_DECIMALS: 18,
  TOKEN_DECIMALS: 6, // For launchpad tokens
  DEFAULT_THRESHOLD: ethers.parseEther('0.03'), // 0.03 HYPE
  MINIMUM_THRESHOLD: ethers.parseEther('0.02'), // 0.02 HYPE
};

export class EVMClientUtils {
  static getProvider(attempt: number = 0): ethers.JsonRpcProvider {
    const rpcUrls = [
      'https://rpc.hyperliquid.xyz/evm',
      // Add backup RPC URLs here if available
    ];

    const rpcUrl = rpcUrls[attempt] || rpcUrls[0];
    return new ethers.JsonRpcProvider(rpcUrl);
  }

  static async getBlockNumber(): Promise<number> {
    let provider = evmProvider;
    return await retry(
      async () => {
        return await provider.getBlockNumber();
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(`getBlockNumber retry ${attempt}`, (e as Error).message);
          provider = EVMClientUtils.getProvider(attempt);
        },
      },
    );
  }

  static async getBalance(address: string): Promise<bigint> {
    let provider = evmProvider;
    return await retry(
      async () => {
        return await provider.getBalance(address);
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(`getBalance retry ${attempt}`, (e as Error).message);
          provider = EVMClientUtils.getProvider(attempt);
        },
      },
    );
  }

  static async getTransaction(
    txHash: string,
  ): Promise<ethers.TransactionResponse | null> {
    let provider = evmProvider;
    return await retry(
      async () => {
        return await provider.getTransaction(txHash);
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(`getTransaction retry ${attempt}`, (e as Error).message);
          provider = EVMClientUtils.getProvider(attempt);
        },
      },
    );
  }

  static async getTransactionReceipt(
    txHash: string,
  ): Promise<ethers.TransactionReceipt | null> {
    let provider = evmProvider;
    return await retry(
      async () => {
        return await provider.getTransactionReceipt(txHash);
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(
            `getTransactionReceipt retry ${attempt}`,
            (e as Error).message,
          );
          provider = EVMClientUtils.getProvider(attempt);
        },
      },
    );
  }

  static async getLogs(filter: ethers.Filter): Promise<ethers.Log[]> {
    let provider = evmProvider;
    return await retry(
      async () => {
        return await provider.getLogs(filter);
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(`getLogs retry ${attempt}`, (e as Error).message);
          provider = EVMClientUtils.getProvider(attempt);
        },
      },
    );
  }

  static async getOwnerTokenBalances(
    owner: string,
    type: string[] = ['ERC-20'],
  ): Promise<
    {
      coinType: string;
      totalBalance: string;
      decimals: number;
      name: string;
      symbol: string;
      iconUrl: string;
    }[]
  > {
    const response = await axios.get(
      `https://www.hyperscan.com/api/v2/addresses/${owner}/tokens?type=${type.join(',')}`,
      {
        headers: {
          accept: 'application/json',
        },
      },
    );
    const tokens = response.data.items || [];

    return tokens.map((token: any) => ({
      coinType: token.token.address,
      totalBalance: token.value,
      decimals: token.token.decimals,
      name: token.token.name,
      symbol: token.token.symbol,
      iconUrl: token.token.icon_url,
    }));
  }
}

// Export the provider for direct use
export { evmProvider as default };
