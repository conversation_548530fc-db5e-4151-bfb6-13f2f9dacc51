export const autoImport = (module) => {
  return Object.keys(module).map((moduleName) => module[moduleName]);
};

export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export async function callWithChunk<T, V>(
  events: T[],
  callback: (array: T[]) => Promise<V>,
  chunk_size = 50,
) {
  if (!Array.isArray(events)) {
    console.warn('callWithChunk: items must be an array.');
    return;
  }

  const eventsLength = events.length;
  for (let index = 0; index < eventsLength; index += chunk_size) {
    const eventsChunk = events.slice(index, index + chunk_size);
    console.debug(
      `callWithChunk: processing ${callback.name} ${index + eventsChunk.length}/${eventsLength}`,
    );

    await callback(eventsChunk);
  }
}
