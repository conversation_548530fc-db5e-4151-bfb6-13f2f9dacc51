const DEFAULT_SERVER_PORT = 3000;
const DEFAULT_WS_SERVER_PORT = 3001;

export default () => ({
  app: {
    port: parseInt(process.env.APP_PORT as string, 10) || DEFAULT_SERVER_PORT,
    ws_port:
      parseInt(process.env.WS_PORT as string, 10) || DEFAULT_WS_SERVER_PORT,
    prefix: process.env.APP_PREFIX,
    env: process.env.NODE_ENV || 'local',
    evmRpcUrl: process.env.EVM_RPC_URL || 'https://rpc.hyperliquid.xyz/evm',
    shroTokenAddress: process.env.SHRO_TOKEN_ADDRESS,
    moonbagsLaunchpadAddress: process.env.MOONBAGS_LAUNCHPAD_ADDRESS,
    moonbagsStakeAddress: process.env.MOONBAGS_STAKE_ADDRESS,
    tokenLockAddress: process.env.TOKEN_LOCK_ADDRESS,
    shroTreasuryAddress: process.env.SHRO_TREASURY_ADDRESS,
  },
});
