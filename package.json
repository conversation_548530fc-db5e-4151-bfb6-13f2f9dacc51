{"name": "moonbags-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start:api": "nest start --watch", "start:ws": "ts-node -r tsconfig-paths/register ./scripts/ws", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:kingOfHill": "nest start kingOfHill", "start:cct": "nest start coinConsumerTrade", "start:indexer-treasury": "ts-node -r tsconfig-paths/register ./scripts/indexer-treasury", "start:indexer-coin-holder": "ts-node -r tsconfig-paths/register ./scripts/indexer-coin-holder", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky"}, "dependencies": {"@faker-js/faker": "^9.4.0", "@google-cloud/storage": "^7.15.2", "@keyv/redis": "^4.2.0", "@liaoliaots/nestjs-redis": "^10.0.0", "@nest-lab/throttler-storage-redis": "^1.1.0", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.1", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^11.0.8", "@nestjs/schedule": "4.1.2", "@nestjs/swagger": "^11.0.3", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.0.8", "@pinata/sdk": "^2.1.0", "@privy-io/server-auth": "^1.18.8", "@socket.io/redis-adapter": "^8.3.0", "@socket.io/redis-emitter": "^5.1.0", "@types/jwt-decode": "^3.1.0", "@types/multer": "^1.4.12", "@types/passport-jwt": "^4.0.1", "async-retry": "^1.3.3", "axios": "^1.7.9", "bignumber.js": "^9.1.2", "bs58": "^6.0.0", "cache-manager": "^6.4.0", "dotenv": "^17.0.1", "ethers": "^6.13.5", "execution-time": "^1.4.1", "install": "^0.13.0", "ioredis": "^5.4.2", "jwt-decode": "^4.0.0", "kafkajs": "^2.2.4", "moment": "^2.30.1", "mongoose": "^8.9.6", "mongoose-aggregate-paginate-v2": "^1.1.3", "mongoose-paginate-v2": "^1.9.0", "multer": "^1.4.5-lts.1", "nanoid": "3.3.11", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pinata-web3": "^0.5.4", "redis": "^4.7.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "slugify": "^1.6.6", "socket.io": "^4.8.1", "tweetnacl": "^1.0.3", "typeorm": "^0.3.20", "viem": "^2.23.2"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/async-retry": "^1.4.9", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.16", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.1.7", "jest": "^29.5.0", "lint-staged": "^15.4.3", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.ts": ["yarn lint", "yarn format"]}}